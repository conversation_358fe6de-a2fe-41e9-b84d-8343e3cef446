﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderDetailConfiguration : IEntityTypeConfiguration<OrderDetail>
{
       public void Configure(EntityTypeBuilder<OrderDetail> builder)
       {
              builder.ToTable("OrderDetails");

              builder.HasKey(od => od.Id);

              builder.Property(od => od.Id)
                     .ValueGeneratedNever(); // Assuming ID is a Guid and set manually

              builder.Property(od => od.Description)
                     .IsRequired()
                     .HasMaxLength(500);

              builder.Property(od => od.Status)
                     .IsRequired();

              builder.ComplexProperty(
                     od => od.Iban, buildAction =>
                     {
                            buildAction.Property(p => p.Value)
                                       .HasColumnName("Iban")
                                       .IsRequired();
                     });

              builder.ComplexProperty(
                     od => od.WageTransferTransactionId, buildAction =>
                     {
                            buildAction.Property(p => p!.Value.Value)
                                       .HasColumnName("WageTransferTransactionId")
                                       .IsRequired(false);
                     });

              builder.Property(od => od.Amount)
                     .IsRequired()
                     .HasPrecision(24, 8);

              builder.Property(od => od.WageAmount)
                     .IsRequired()
                     .HasPrecision(24, 8);

              builder.HasOne(od => od.Order)
                     .WithMany(o => o.OrderDetails)
                     .HasForeignKey(od => od.OrderId)
                     .OnDelete(DeleteBehavior.Cascade);
               
               builder.HasOne(od => od.OrderDetailRollbackInfo)
                      .WithOne(ri => ri.OrderDetail)
                      .HasForeignKey<OrderDetailRollbackInfo>(ri => ri.OrderDetailId)
                      .IsRequired(false);
    }
}
