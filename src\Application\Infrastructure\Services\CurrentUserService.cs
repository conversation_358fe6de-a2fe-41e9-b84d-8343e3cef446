using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Zify.Settlement.Application.Common.Interfaces;

namespace Zify.Settlement.Application.Infrastructure.Services;

public class CurrentUserService(IHttpContextAccessor httpContextAccessor) : ICurrentUserService
{
    public int? UserId
    {
        get
        {
            var userIdClaim = httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                              httpContextAccessor.HttpContext?.User.FindFirst("sub")?.Value ??
                              httpContextAccessor.HttpContext?.User.FindFirst("userId")?.Value;

            if (string.IsNullOrEmpty(userIdClaim))
                return null;

            return int.TryParse(userIdClaim, out var userId) ? userId : null;
        }
    }
}