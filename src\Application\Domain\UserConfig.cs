﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;

namespace Zify.Settlement.Application.Domain;

public class UserConfig : AuditableEntity
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string AcceptorCode { get; set; }
    public bool IsCritical { get; set; }
    public bool IsFree { get; set; }
    public bool IsDepositActivate { get; set; }
    public bool IsBanned { get; set; }
    public WageType WageType { get; set; }
    public decimal WageValue { get; set; }
    public int Max { get; set; }
    public int Min { get; set; }
    public int MaxSettlementAmount { get; set; }

    public SettlementPlanType PlanType { get; set; }
    public bool AllowSettlementRegistration { get; set; }

    public bool AuthenticatorTotpEnabled { get; set; }
    public string AuthenticatorTotpSecretKey { get; set; }

    public UserWalletInformation WalletInformation { get; set; } 
    
    public static UserConfig Create(UserWalletInformation walletInformation)
    {
        Guard.Against.Null(walletInformation, nameof(walletInformation));
        return new UserConfig
        {
            UserId = walletInformation.UserId,
            WalletInformation = walletInformation,
            
            AcceptorCode = string.Empty,
            IsCritical = false,
            IsFree = false,
            IsDepositActivate = false,
            IsBanned = false,
            WageType = WageType.Fixed,
            WageValue = 0,
            Max = 0,
            Min = 0,
            MaxSettlementAmount = 0,
            PlanType = SettlementPlanType.Simple,
            AllowSettlementRegistration = false,
            AuthenticatorTotpEnabled = false,
            AuthenticatorTotpSecretKey = string.Empty,
        };
    }
}

public enum SettlementPlanType
{
    Simple,
    Advance
}

public enum WageType
{
    Fixed,
    Percent
}