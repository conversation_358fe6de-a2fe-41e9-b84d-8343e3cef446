using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class UserWalletInformationConfiguration : IEntityTypeConfiguration<UserWalletInformation>
{
    public void Configure(EntityTypeBuilder<UserWalletInformation> builder)
    {
        builder.ToTable("UserWalletInformations");

        builder.HasKey(wi => wi.Id);

        builder.Property(wi => wi.Id)
               .ValueGeneratedOnAdd();

        builder.Property(wi => wi.UserId)
               .IsRequired();

        builder.ComplexProperty(
               wi => wi.SettlementWalletId, buildAction =>
               {
                    buildAction.Property(p => p!.Value.Value)
                              .HasColumnName("SettlementWalletId")
                              .IsRequired(false);
               });

        builder.ComplexProperty(
               wi => wi.PaymentWalletId, buildAction =>
               {
                    buildAction.Property(p => p.Value)
                              .HasColumnName("PaymentWalletId")
                              .IsRequired();
               });
    }
}