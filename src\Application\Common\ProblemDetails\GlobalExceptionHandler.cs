﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Zify.Settlement.Application.Domain.Exceptions;

namespace Zify.Settlement.Application.Common.ProblemDetails;

public class GlobalExceptionHandler : IExceptionHandler
{
    private readonly IProblemDetailsService _problemDetailsService;
    private readonly Dictionary<Type, Func<Exception, HttpContext, Task<bool>>> _handlers;
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(IProblemDetailsService problemDetailsService, ILogger<GlobalExceptionHandler> logger)
    {
        _problemDetailsService = problemDetailsService;
        _logger = logger;
        _handlers = new Dictionary<Type, Func<Exception, HttpContext, Task<bool>>>
        {
            { typeof(InvalidIbanException), HandleInvalidIbanExceptionAsync },
            { typeof(InvalidWalletIdException), HandleInvalidWalletIdExceptionAsync },
            { typeof(InvalidCorrelationIdException), HandleInvalidCorrelationIdExceptionAsync },
            { typeof(InvalidOperationException), HandleInvalidOperationExceptionAsync },
            { typeof(ArgumentException), HandleArgumentExceptionAsync },
        };
    }

    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        var exceptionType = exception.GetType();

        if (_handlers.TryGetValue(exceptionType, out var handler))
        {
            return await handler(exception, httpContext);
        }

        // If we still don't have a handler, use the default one
        return await HandleDefaultExceptionAsync(exception, httpContext);
    }

    private async Task<bool> HandleInvalidIbanExceptionAsync(Exception exception, HttpContext httpContext)
    {
        var invalidIbanException = (InvalidIbanException)exception;
        _logger.LogWarning("Invalid IBAN exception: {Iban}", invalidIbanException.Iban);

        return await WriteProblemDetailsAsync(
            httpContext,
            "Invalid IBAN",
            invalidIbanException.Message,
            StatusCodes.Status400BadRequest);
    }

    private async Task<bool> HandleInvalidWalletIdExceptionAsync(Exception exception, HttpContext httpContext)
    {
        var invalidWalletIdException = (InvalidWalletIdException)exception;
        _logger.LogWarning("Invalid Wallet ID exception: {WalletId}", invalidWalletIdException.WalletId);

        return await WriteProblemDetailsAsync(
            httpContext,
            "Invalid Wallet ID",
            invalidWalletIdException.Message,
            StatusCodes.Status400BadRequest);
    }

    private async Task<bool> HandleInvalidCorrelationIdExceptionAsync(Exception exception, HttpContext httpContext)
    {
        var invalidCorrelationIdException = (InvalidCorrelationIdException)exception;
        _logger.LogWarning("Invalid Correlation ID exception: {CorrelationId}", invalidCorrelationIdException.CorrelationId);

        return await WriteProblemDetailsAsync(
            httpContext,
            "Invalid Correlation ID",
            invalidCorrelationIdException.Message,
            StatusCodes.Status400BadRequest);
    }

    private async Task<bool> HandleInvalidOperationExceptionAsync(Exception exception, HttpContext httpContext)
    {
        _logger.LogWarning("Invalid operation exception: {Message}", exception.Message);

        return await WriteProblemDetailsAsync(
            httpContext,
            "Invalid Operation",
            exception.Message,
            StatusCodes.Status400BadRequest);
    }

    private async Task<bool> HandleArgumentExceptionAsync(Exception exception, HttpContext httpContext)
    {
        _logger.LogWarning("Argument exception: {Message}", exception.Message);

        return await WriteProblemDetailsAsync(
            httpContext,
            "Invalid Argument",
            exception.Message,
            StatusCodes.Status400BadRequest);
    }

    private async Task<bool> HandleDefaultExceptionAsync(Exception exception, HttpContext httpContext)
    {
        _logger.LogError(exception, "An unhandled exception has occurred");

        return await WriteProblemDetailsAsync(
            httpContext,
            "Internal Server Error",
            "An unexpected error occurred. Please try again later.",
            StatusCodes.Status500InternalServerError);
    }

    private async Task<bool> WriteProblemDetailsAsync(HttpContext httpContext, string title, string detail, int statusCode)
    {
        httpContext.Response.StatusCode = statusCode;

        await _problemDetailsService.WriteAsync(new ProblemDetailsContext
        {
            HttpContext = httpContext,
            ProblemDetails =
            {
                Status = statusCode,
                Title = title,
                Detail = detail,
                Type = $"https://httpstatuses.com/{statusCode}"
            }
        });

        return true;
    }
}
