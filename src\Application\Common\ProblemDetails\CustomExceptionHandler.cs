using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Zify.Settlement.Application.Common.ProblemDetails;

public static class CustomExceptionHandler
{
    public static WebApplication UseCustomExceptionHandler(this WebApplication app)
    {
        // Configure status code pages for non-exception error responses (e.g., 404, 401, etc.)
        app.UseStatusCodePages(statusCodeHandlerApp =>
        {
            statusCodeHandlerApp.Run(async context =>
            {
                context.Response.ContentType = "application/problem+json";

                if (context.RequestServices.GetService<IProblemDetailsService>() is { } problemDetailsService)
                {
                    await problemDetailsService.WriteAsync(new ProblemDetailsContext
                    {
                        HttpContext = context,
                        ProblemDetails =
                        {
                            Detail = ReasonPhrases.GetReasonPhrase(context.Response.StatusCode),
                            Status = context.Response.StatusCode,
                            Title = ReasonPhrases.GetReasonPhrase(context.Response.StatusCode),
                            Type = $"https://httpstatuses.com/{context.Response.StatusCode}"
                        }
                    });
                }
            });
        });

        // Configure exception handler middleware
        app.UseExceptionHandler();

        // Hide detailed exception information in production
        if (!app.Environment.IsDevelopment())
        {
            app.UseHsts();
        }

        return app;
    }
}
