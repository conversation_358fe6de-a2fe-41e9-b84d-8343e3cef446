﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class UserWalletInformation : AuditableEntity
{
    public int Id { get; private init; }
    public int UserId { get; private set; }
    public WalletId? SettlementWalletId { get; private set; }
    public WalletId PaymentWalletId { get; private set; }

    private UserWalletInformation()
    {
    }

    public static UserWalletInformation Create(int userId, Guid paymentWalletId)
    {
        Guard.Against.NegativeOrZero(userId, nameof(userId));
        Guard.Against.NullOrEmpty(paymentWalletId, nameof(paymentWalletId));
        return new UserWalletInformation
        {
            UserId = userId,
            PaymentWalletId = WalletId.Of(paymentWalletId)
        };
    }

    public void UpdatePaymentWalletId(Guid paymentWalletId)
    {
        Guard.Against.NullOrEmpty(paymentWalletId, nameof(paymentWalletId));
        PaymentWalletId = WalletId.Of(paymentWalletId);
    }

    public void SetSettlementWalletId(Guid settlementWalletId)
    {
        Guard.Against.NullOrEmpty(settlementWalletId, nameof(settlementWalletId));
        SettlementWalletId = WalletId.Of(settlementWalletId);
    }
}