﻿using System.Text;
using Zify.Settlement.Application.Domain.Exceptions;

namespace Zify.Settlement.Application.Domain.ValueObjects;

public readonly record struct Iban
{
    public string Value { get; }

    private Iban(string value) => Value = value;

    public static Iban Of(string value)
    {
        if (string.IsNullOrWhiteSpace(value) || !IsValidIbanFormat(value))
        {
            throw new InvalidIbanException(value);
        }

        return new Iban(value);
    }

    public string ToNormalizedString()
    {
        if (!Value.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
        {
            return "IR" + Value.Replace(" ", "")
                .Replace("-", "")
                .Replace("_", "").ToUpper();
        }

        return Value.Replace(" ", "")
            .Replace("-", "")
            .Replace("_", "").ToUpper();
    }

    public bool Equals(Iban other)
    {
        return string.Equals(
            ToNormalizedString(),
            other.ToNormalizedString(),
            StringComparison.OrdinalIgnoreCase);
    }

    public override int GetHashCode()
    {
        return ToNormalizedString().GetHashCode(StringComparison.OrdinalIgnoreCase);
    }

    public static implicit operator string(Iban iban)
    {
        return iban.Value;
    }

    public override string ToString() => Value;

    private static bool IsValidIbanFormat(string iban)
    {
        // Remove spaces and convert to uppercase
        iban = iban.Replace(" ", "")
            .Replace("-", "")
            .Replace("_", "").ToUpper();

        // Check if it's an Iranian IBAN (starts with IR and has 26 characters)
        if (!iban.StartsWith("IR") || iban.Length != 26)
            return false;

        // Check if all characters after country code are digits
        if (!iban[2..].All(char.IsDigit))
            return false;

        try
        {
            // Move the first 4 characters to the end
            var rearranged = string.Concat(iban.AsSpan(4), iban.AsSpan(0, 4));

            // Convert letters to numbers (A=10, B=11, ..., Z=35)
            var converted = new StringBuilder();
            foreach (var c in rearranged)
            {
                if (char.IsLetter(c))
                    converted.Append(c - 'A' + 10);
                else
                    converted.Append(c);
            }

            // Calculate modulo 97 using BigInteger (since the number is too large for long)
            var number = System.Numerics.BigInteger.Parse(converted.ToString());
            return number % 97 == 1;
        }
        catch
        {
            return false;
        }
    }
}