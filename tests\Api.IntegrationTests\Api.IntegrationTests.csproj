﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<AssemblyName>PayPing.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
		<PackageReference Include="coverlet.collector" Version="6.0.2" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
		<PackageReference Include="xunit" Version="2.9.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\src\Api\Api.csproj" />
	  <ProjectReference Include="..\..\src\Application\Application.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

</Project>
